# 🐳 Hướng dẫn chạy Na Food với Docker

## 📋 Yêu cầu

- Docker Desktop (Windows/Mac) hoặc Docker Engine (Linux)
- Docker Compose (thường đi kèm với Docker Desktop)

## 🚀 Cách chạy nhanh nhất

### 1. Chạy chỉ ứng dụng <PERSON> (khuyến nghị)
```bash
# Build và chạy ứng dụng
docker-compose up na-food-app

# Chạy trong background
docker-compose up -d na-food-app
```

### 2. Chạy đầy đủ với MongoDB local
```bash
# Chạy tất cả services (app + MongoDB + Mongo Express)
docker-compose up

# Chạy trong background
docker-compose up -d
```

## 🔧 Các lệnh Docker hữu ích

### Quản lý containers
```bash
# Xem containers đang chạy
docker-compose ps

# Xem logs
docker-compose logs na-food-app
docker-compose logs -f na-food-app  # Follow logs

# Dừng containers
docker-compose down

# Rebuild và restart
docker-compose up --build na-food-app
```

### Build trực tiế<PERSON>
```bash
# Build Docker image
docker build -t nafood-app .

# Chạy container
docker run -d -p 5000:5000 --name nafood-container nafood-app

# Dừng và xóa container
docker stop nafood-container
docker rm nafood-container
```

## 🌐 Truy cập ứng dụng

- **Na Food App**: http://localhost:5000
- **MongoDB Express** (nếu chạy): http://localhost:8081
  - Username: admin
  - Password: admin123

## 🔑 Tài khoản mặc định

- **Admin**: <EMAIL> / 123456

## ⚙️ Cấu hình môi trường

Ứng dụng sử dụng các biến môi trường sau:
- `NODE_ENV=production`
- `PORT=5000`
- `DATABASE_URL=mongodb+srv://admin:<EMAIL>/`
- `JWT_SECRET=your-production-jwt-secret-key-here`

## 🛠️ Troubleshooting

### Port đã được sử dụng
```bash
# Thay đổi port mapping
docker-compose up -d -p 5001:5000 na-food-app
```

### Rebuild từ đầu
```bash
# Xóa containers và volumes
docker-compose down -v

# Rebuild không cache
docker-compose build --no-cache na-food-app

# Chạy lại
docker-compose up na-food-app
```

### Xem logs chi tiết
```bash
# Logs của container cụ thể
docker logs <container-name>

# Logs realtime
docker-compose logs -f na-food-app
```

## 📝 Lưu ý

- Lần build đầu tiên có thể mất 3-5 phút
- Ứng dụng sẽ tự động restart nếu crash
- Health check được thực hiện mỗi 30 giây
- Sử dụng MongoDB cloud mặc định (không cần setup local DB)
