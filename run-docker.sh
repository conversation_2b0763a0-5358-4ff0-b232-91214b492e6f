#!/bin/bash

echo "🐳 Starting Na Food Docker Container..."

# Stop and remove existing container if exists
docker stop nafood-container 2>/dev/null || true
docker rm nafood-container 2>/dev/null || true

# Run the container
docker run -d \
  --name nafood-container \
  -p 5000:5000 \
  -e DATABASE_URL="mongodb+srv://admin:<EMAIL>/" \
  -e JWT_SECRET="your-jwt-secret-key-here" \
  -e NODE_ENV="production" \
  nafood-app

if [ $? -eq 0 ]; then
    echo "✅ Na Food is running in Docker!"
    echo "🌐 Open your browser and go to: http://localhost:5000"
    echo ""
    echo "To view logs: docker logs nafood-container"
    echo "To stop: docker stop nafood-container"
else
    echo "❌ Failed to start container"
    exit 1
fi
