services:
  na-food-app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - DATABASE_URL=mongodb+srv://admin:<EMAIL>/
      - JWT_SECRET=your-production-jwt-secret-key-here
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: MongoDB local (if you want local database)
  mongo:
    image: mongo:7.0
    container_name: na-food-mongodb
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: na_food
    volumes:
      - mongo_data:/data/db
    restart: unless-stopped

  # Optional: MongoDB Admin UI
  mongo-express:
    image: mongo-express:1.0.0
    container_name: na-food-mongo-express
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: ***************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    depends_on:
      - mongo
    restart: unless-stopped

volumes:
  mongo_data:
    driver: local
