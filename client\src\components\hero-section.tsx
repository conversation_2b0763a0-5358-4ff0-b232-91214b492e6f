"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { Banner } from "@shared/schema";
import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

export default function HeroSection() {
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);

  const { data: banners = [] } = useQuery<Banner[]>({
    queryKey: ["/api/banners"],
    queryFn: async () => {
      const response = await fetch("/api/banners?active=true");
      return response.json();
    },
  });

  // Auto slide
  useEffect(() => {
    if (banners.length > 1) {
      const interval = setInterval(() => {
        setCurrentBannerIndex((prev) => (prev + 1) % banners.length);
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [banners.length]);

  const handleOrderNow = () => {
    document.getElementById("menu")?.scrollIntoView({ behavior: "smooth" });
  };

  const nextBanner = () => {
    setCurrentBannerIndex((prev) => (prev + 1) % banners.length);
  };

  const prevBanner = () => {
    setCurrentBannerIndex((prev) => (prev - 1 + banners.length) % banners.length);
  };

  const mainBanner = banners[currentBannerIndex] || banners[0];

  return (
    <section className="relative w-full h-[500px] bg-white overflow-hidden">
      <div className="max-w-7xl mx-auto h-full flex items-stretch">
        {/* Left Side - Content */}
        <div className="w-full md:w-2/5 flex items-center px-8 sm:px-12 lg:px-16 z-10">
          <div className="text-black max-w-xl">
            <h1 className="text-5xl md:text-6xl font-bold mb-4 leading-tight font-nunito">
              {mainBanner?.title || "VY FOOD"}
            </h1>
            <p className="text-lg md:text-xl mb-8 font-medium leading-relaxed italic text-gray-700">
              {mainBanner?.description || "Tinh hoa ẩm thực"}
            </p>
            <Button
              onClick={handleOrderNow}
              className="bg-primary hover:bg-primary/90 text-white font-bold px-8 py-3 text-lg rounded-lg hover:scale-105 transition-all duration-300 shadow-md"
            >
              ĐẶT HÀNG NGAY
            </Button>
          </div>
        </div>

        {/* Right Side - Banner Image */}
        <div className="w-3/5 relative h-full">
          <div
            className="w-full h-full transition-all duration-1000 ease-in-out rounded-l-3xl overflow-hidden"
            style={{
              backgroundImage: mainBanner?.image
                ? `url(${mainBanner.image})`
                : "url('https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=800')",
              backgroundSize: "cover",
              backgroundPosition: "center center",
              backgroundRepeat: "no-repeat",
            }}
          />
          <div className="absolute inset-0 bg-black/20 rounded-l-3xl"></div>

          {/* Navigation Arrows */}
          {banners.length > 1 && (
            <>
              <button
                onClick={prevBanner}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/30 backdrop-blur-sm hover:bg-white/50 text-white p-2 rounded-full z-20"
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              <button
                onClick={nextBanner}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/30 backdrop-blur-sm hover:bg-white/50 text-white p-2 rounded-full z-20"
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </>
          )}

          {/* Indicators */}
          {banners.length > 1 && (
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20">
              {banners.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentBannerIndex(index)}
                  className={`w-2.5 h-2.5 rounded-full ${
                    index === currentBannerIndex
                      ? "bg-white scale-110"
                      : "bg-white/50 hover:bg-white/75"
                  } transition-all`}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  );
}
