import { But<PERSON> } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { Banner } from "@shared/schema";
import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

export default function HeroSection() {
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);

  const { data: banners = [] } = useQuery<Banner[]>({
    queryKey: ["/api/banners"],
    queryFn: async () => {
      const response = await fetch("/api/banners?active=true");
      return response.json();
    },
  });

  // Auto-slide banners
  useEffect(() => {
    if (banners.length > 1) {
      const interval = setInterval(() => {
        setCurrentBannerIndex((prev) => (prev + 1) % banners.length);
      }, 5000); // Change banner every 5 seconds

      return () => clearInterval(interval);
    }
  }, [banners.length]);

  const handleOrderNow = () => {
    document.getElementById('menu')?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleViewMenu = () => {
    document.getElementById('menu')?.scrollIntoView({ behavior: 'smooth' });
  };

  const nextBanner = () => {
    setCurrentBannerIndex((prev) => (prev + 1) % banners.length);
  };

  const prevBanner = () => {
    setCurrentBannerIndex((prev) => (prev - 1 + banners.length) % banners.length);
  };

  const mainBanner = banners[currentBannerIndex] || banners[0];

  return (
    <section className="relative w-full h-[500px] bg-gradient-to-br from-primary via-red-600 to-orange-600 overflow-hidden">
      {/* Overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/30"></div>

      {/* Background Image */}
      <div
        className="absolute inset-0 transition-all duration-1000 ease-in-out"
        style={{
          backgroundImage: mainBanner?.image
            ? `url(${mainBanner.image})`
            : "url('https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=800')",
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          width: '100%',
          height: '100%'
        }}
      />

      {/* Navigation Arrows */}
      {banners.length > 1 && (
        <>
          <button
            onClick={prevBanner}
            className="absolute left-16 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300 z-10"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
          <button
            onClick={nextBanner}
            className="absolute right-16 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300 z-10"
          >
            <ChevronRight className="h-6 w-6" />
          </button>
        </>
      )}

      {/* Banner Indicators */}
      {banners.length > 1 && (
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
          {banners.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentBannerIndex(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentBannerIndex
                  ? 'bg-white scale-125'
                  : 'bg-white/50 hover:bg-white/75'
              }`}
            />
          ))}
        </div>
      )}

      {/* Decorative Elements */}
      <div className="absolute top-10 right-16 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
      <div className="absolute bottom-20 left-16 w-24 h-24 bg-orange-300/20 rounded-full blur-lg"></div>

      <div className="relative w-full mx-auto px-8 sm:px-16 lg:px-24 h-full flex items-center">
        <div className="text-white max-w-2xl">
          <h1 className="text-6xl md:text-8xl font-nunito font-bold mb-4 leading-tight">
            {mainBanner?.title || "VY FOOD"}
          </h1>

          <p className="text-xl md:text-2xl mb-8 font-medium leading-relaxed opacity-90 italic">
            {mainBanner?.description || "Tinh hoa ẩm thực"}
          </p>

          <Button
            onClick={handleOrderNow}
            className="bg-primary hover:bg-primary/90 text-white font-bold px-10 py-4 text-lg rounded-lg hover:scale-105 transition-all duration-300 shadow-xl"
          >
            ĐẶT HÀNG NGAY
          </Button>
        </div>
      </div>
    </section>
  );
}
