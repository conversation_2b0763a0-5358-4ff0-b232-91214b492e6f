import { But<PERSON> } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { Banner } from "@shared/schema";
import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

export default function HeroSection() {
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);

  const { data: banners = [] } = useQuery<Banner[]>({
    queryKey: ["/api/banners"],
    queryFn: async () => {
      const response = await fetch("/api/banners?active=true");
      return response.json();
    },
  });

  // Auto-slide banners
  useEffect(() => {
    if (banners.length > 1) {
      const interval = setInterval(() => {
        setCurrentBannerIndex((prev) => (prev + 1) % banners.length);
      }, 5000); // Change banner every 5 seconds

      return () => clearInterval(interval);
    }
  }, [banners.length]);

  const handleOrderNow = () => {
    document.getElementById('menu')?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleViewMenu = () => {
    document.getElementById('menu')?.scrollIntoView({ behavior: 'smooth' });
  };

  const nextBanner = () => {
    setCurrentBannerIndex((prev) => (prev + 1) % banners.length);
  };

  const prevBanner = () => {
    setCurrentBannerIndex((prev) => (prev - 1 + banners.length) % banners.length);
  };

  const mainBanner = banners[currentBannerIndex] || banners[0];

  return (
    <section className="relative w-full bg-gradient-to-br from-primary via-red-600 to-orange-600 overflow-hidden">
      {/* Overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/30 z-10"></div>

      {/* Background Image with aspect ratio */}
      <div className="relative w-full">
        <img
          src={mainBanner?.image || "https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=800"}
          alt={mainBanner?.title || "Banner"}
          className="w-full h-auto object-cover transition-all duration-1000 ease-in-out"
          style={{
            minHeight: '250px',
            maxHeight: '500px'
          }}
        />

        {/* Content overlay */}
        <div className="absolute inset-0 z-20">

        {/* Navigation Arrows */}
        {banners.length > 1 && (
          <>
            <button
              onClick={prevBanner}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300 z-30"
            >
              <ChevronLeft className="h-6 w-6" />
            </button>
            <button
              onClick={nextBanner}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300 z-30"
            >
              <ChevronRight className="h-6 w-6" />
            </button>
          </>
        )}

        {/* Banner Indicators */}
        {banners.length > 1 && (
          <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2 z-30">
            {banners.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentBannerIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentBannerIndex
                    ? 'bg-white scale-125'
                    : 'bg-white/50 hover:bg-white/75'
                }`}
              />
            ))}
          </div>
        )}

        {/* Decorative Elements */}
        <div className="absolute top-10 right-10 w-32 h-32 bg-white/10 rounded-full blur-xl z-20"></div>
        <div className="absolute bottom-20 left-20 w-24 h-24 bg-orange-300/20 rounded-full blur-lg z-20"></div>

        {/* Content */}
        <div className="absolute inset-0 flex items-center z-25">
          <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 w-full">
            <div className="text-white max-w-2xl">
              {/* Badge */}
              <div className="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium mb-6 border border-white/30">
                <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                Khuyến mãi đặc biệt
              </div>

              <h1 className="text-4xl md:text-6xl font-nunito font-bold mb-6 leading-tight">
                {mainBanner?.title || (
                  <>
                    Khuyến mãi<br />
                    <span className="text-yellow-300 drop-shadow-lg">đặc biệt</span>
                  </>
                )}
              </h1>

              <p className="text-lg md:text-xl mb-8 font-medium leading-relaxed opacity-90">
                {mainBanner?.description || "Giảm 20% cho đơn hàng đầu tiên"}
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  onClick={handleOrderNow}
                  className="bg-white text-primary font-bold px-8 py-3 text-base rounded-xl hover:bg-gray-100 hover:scale-105 transition-all duration-300 shadow-xl"
                >
                  Đặt hàng ngay
                </Button>
                <Button
                  variant="outline"
                  onClick={handleViewMenu}
                  className="border-2 border-white text-white font-bold px-8 py-3 text-base rounded-xl hover:bg-white hover:text-primary hover:scale-105 transition-all duration-300 backdrop-blur-sm"
                >
                  Xem thực đơn
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
